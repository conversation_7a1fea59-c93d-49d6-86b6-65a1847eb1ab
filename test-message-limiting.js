/**
 * Test script to verify message limiting functionality
 */

import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { count_tokens_approximately, trim_messages } from '@langchain/core/messages/utils';

// Test configuration
const TEST_CONFIG = {
	MAX_TOKENS: 100, // Very low limit for testing
	STRATEGY: 'last',
	START_ON: 'human',
	END_ON: ['human', 'tool'],
};

// Create test messages
const testMessages = [
	new HumanMessage('Hello, how are you?'),
	new AIMessage("I'm doing well, thank you! How can I help you today?"),
	new HumanMessage('Can you tell me about the weather?'),
	new AIMessage(
		"I'd be happy to help with weather information, but I don't have access to real-time weather data. You might want to check a weather website or app for current conditions.",
	),
	new HumanMessage('What about machine learning?'),
	new AIMessage(
		"Machine learning is a fascinating field! It's a subset of artificial intelligence that focuses on algorithms and statistical models that enable computers to improve their performance on a specific task through experience, without being explicitly programmed for every scenario.",
	),
	new HumanMessage("That's interesting. Can you give me more details?"),
];

console.log('Original messages count:', testMessages.length);
console.log('Original total tokens (approximate):', count_tokens_approximately(testMessages));

// Test the trim_messages function
const trimmedMessages = trim_messages(testMessages, {
	strategy: TEST_CONFIG.STRATEGY,
	token_counter: count_tokens_approximately,
	max_tokens: TEST_CONFIG.MAX_TOKENS,
	start_on: TEST_CONFIG.START_ON,
	end_on: TEST_CONFIG.END_ON,
});

console.log('\nAfter trimming:');
console.log('Trimmed messages count:', trimmedMessages.length);
console.log('Trimmed total tokens (approximate):', count_tokens_approximately(trimmedMessages));

console.log('\nTrimmed messages:');
trimmedMessages.forEach((msg, index) => {
	console.log(`${index + 1}. [${msg.constructor.name}] ${msg.content.substring(0, 50)}...`);
});

console.log('\nMessage limiting test completed successfully!');
