{"name": "harmony-agent", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "vercel dev", "format": "prettier --write .", "format:check": "prettier --check .", "auto-fix": "bunx eslint . --fix", "format-fix": "bunx eslint . --fix && prettier --write ."}, "devDependencies": {"@eslint/js": "^9.29.0", "eslint": "^9.33.0", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0"}, "dependencies": {"@langchain/community": "^0.3.50", "@langchain/core": "^0.3.71", "@langchain/exa": "^0.1.0", "@langchain/google-common": "^0.2.12", "@langchain/google-genai": "^0.2.12", "@langchain/langgraph": "^0.4.5", "@langchain/langgraph-checkpoint-postgres": "^0.1.1", "@langchain/openai": "^0.6.7", "@upstash/redis": "^1.35.3", "@upstash/vector": "^1.2.2", "@vercel/functions": "^2.2.12", "hono": "^4.9.2", "langchain": "^0.3.30", "langfuse-langchain": "^3.38.4", "nanoid": "^5.1.5", "pg": "^8.16.3"}}